#include "TB6612.h"
#include <math.h>
#include <stdio.h>
#define TURNCAR 38

/**************************************************************************
功能：TB6612初始化函数
入口参数：定时器参数
返回  值：无
**************************************************************************/
void MOTOR_Init()
{
	TB6612_Init(100-1,168-1);  
	motor_mode();
	// 初始化PWM输出为0
	TIM_SetCompare3(TIM3,0); // Motor1	
	TIM_SetCompare4(TIM3,0); // Motor2
	TIM_SetCompare3(TIM4,0); // Motor3    
	TIM_SetCompare4(TIM8,0); // Motor4
	
	// 初始化编码器
	Encoder_Init();
}

void TB6612_Init(int arr, int psc)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStrue;
	TIM_OCInitTypeDef TIM_OCInitTypeStrue;
	
	// 使能所需的GPIO和定时器时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOB | RCC_APB2Periph_GPIOC | RCC_APB2Periph_GPIOD | RCC_APB2Periph_GPIOE, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3 | RCC_APB1Periph_TIM4, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM8, ENABLE); // TIM8是高级定时器，在APB2总线上
	
	// 配置TIM3的PWM输出引脚
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	
	// TIM3 CH3和CH4的引脚配置
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1; // 假设TIM3_CH3和TIM3_CH4对应的是PB0和PB1
	GPIO_Init(GPIOB, &GPIO_InitStructure);
	
	// TIM4 CH3的引脚重映射配置到PD14
	GPIO_PinRemapConfig(GPIO_Remap_TIM4, ENABLE); // 启用TIM4完全重映射
	
	// TIM4 CH3的引脚配置(PD14)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_14;
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	
	// TIM8 CH4的引脚配置
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9; // TIM8_CH4对应的是PC9
	GPIO_Init(GPIOC, &GPIO_InitStructure);

	// 配置TIM3
	TIM_TimeBaseInitStrue.TIM_Period = arr;
	TIM_TimeBaseInitStrue.TIM_Prescaler = psc;
	TIM_TimeBaseInitStrue.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStrue.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM3, &TIM_TimeBaseInitStrue);
	
	// 配置TIM4
	TIM_TimeBaseInit(TIM4, &TIM_TimeBaseInitStrue);
	
	// 配置TIM8
	TIM_TimeBaseInit(TIM8, &TIM_TimeBaseInitStrue);
	
	// 配置PWM模式
	TIM_OCInitTypeStrue.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitTypeStrue.TIM_OCPolarity = TIM_OCPolarity_High;
	TIM_OCInitTypeStrue.TIM_OutputState = TIM_OutputState_Enable;
	
	// 配置TIM3 CH3和CH4
	TIM_OC3Init(TIM3, &TIM_OCInitTypeStrue);
	TIM_OC4Init(TIM3, &TIM_OCInitTypeStrue);
	
	// 配置TIM4 CH3
	TIM_OC3Init(TIM4, &TIM_OCInitTypeStrue);
	
	// 配置TIM8 CH4
	TIM_OC4Init(TIM8, &TIM_OCInitTypeStrue);
	
	// 使能预装载
	TIM_OC3PreloadConfig(TIM3, TIM_OCPreload_Enable);
	TIM_OC4PreloadConfig(TIM3, TIM_OCPreload_Enable);
	TIM_OC3PreloadConfig(TIM4, TIM_OCPreload_Enable);
	TIM_OC4PreloadConfig(TIM8, TIM_OCPreload_Enable);
	
	TIM_ARRPreloadConfig(TIM3, ENABLE);
	TIM_ARRPreloadConfig(TIM4, ENABLE);
	TIM_ARRPreloadConfig(TIM8, ENABLE);
	
	// TIM8作为高级定时器需要额外使能主输出
	TIM_CtrlPWMOutputs(TIM8, ENABLE);
	
	// 使能定时器
	TIM_Cmd(TIM3, ENABLE);
	TIM_Cmd(TIM4, ENABLE);
	TIM_Cmd(TIM8, ENABLE);
}

void motor_mode()
{
	GPIO_InitTypeDef GPIO_InitStructure;
	
	// 配置Motor1的方向控制引脚 - PC4和PC5
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_8;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOC, &GPIO_InitStructure);
	
	// 配置Motor2的方向控制引脚 - PE7和PE8
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOE, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_7 | GPIO_Pin_8;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOE, &GPIO_InitStructure);
	
	// 配置Motor3和Motor4的方向控制引脚 - PD10、PD11和PD15
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOD, ENABLE);
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10 | GPIO_Pin_11 | GPIO_Pin_15;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOD, &GPIO_InitStructure);
	
	// 初始状态设置
	M1 = 1;
	M2 = 1;
	M3 = 1;
	M4 = 1;
	M5 = 1; 
	M6 = 1;
	M7 = 1;
	M8 = 1;
}

/**************************************************************************
功能：设置PWM值
入口参数：PWM值
返回  值：无
**************************************************************************/
void SetPWM(int pwm)
{
  if(pwm>=0) // 正转
  {
		M1=0; // PC4=0
		M2=1; // PC5=1
		TIM_SetCompare3(TIM3, pwm); // 使用TIM3_CH3
  }
  else if(pwm<0) // 反转
  {
		M1=1; // PC4=1
		M2=0; // PC5=0
		TIM_SetCompare3(TIM3, -pwm); // 使用TIM3_CH3
  }
}

void Set_Motor(s32 leftspeed, s32 rightspeed)
{
	// 左侧电机控制 - Motor1和Motor2
	if(leftspeed < 0)
	{
		// Motor1反转
		GPIO_ResetBits(GPIOC, GPIO_Pin_4);
		GPIO_SetBits(GPIOC, GPIO_Pin_5);
		// Motor2反转
		GPIO_ResetBits(GPIOE, GPIO_Pin_7);
		GPIO_SetBits(GPIOE, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM3, -leftspeed); // Motor1
		TIM_SetCompare4(TIM3, -leftspeed); // Motor2
	}
	else if(leftspeed >= 0)
	{
		// Motor1正转
		GPIO_SetBits(GPIOC, GPIO_Pin_4);
		GPIO_ResetBits(GPIOC, GPIO_Pin_5);
		// Motor2正转
		GPIO_SetBits(GPIOE, GPIO_Pin_7);
		GPIO_ResetBits(GPIOE, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM3, leftspeed); // Motor1
		TIM_SetCompare4(TIM3, leftspeed); // Motor2
	}
	
	// 右侧电机控制 - Motor3和Motor4
	if(rightspeed < 0)
	{
		// Motor3反转
		GPIO_ResetBits(GPIOD, GPIO_Pin_10);
		GPIO_SetBits(GPIOD, GPIO_Pin_11);
		// Motor4反转
		GPIO_ResetBits(GPIOD, GPIO_Pin_15);
		GPIO_SetBits(GPIOC, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM4, -rightspeed); // Motor3
		TIM_SetCompare4(TIM8, -rightspeed); // Motor4
	}
	else if(rightspeed >= 0)
	{
		// Motor3正转
		GPIO_SetBits(GPIOD, GPIO_Pin_10);
		GPIO_ResetBits(GPIOD, GPIO_Pin_11);
		// Motor4正转
		GPIO_SetBits(GPIOD, GPIO_Pin_15);
		GPIO_ResetBits(GPIOC, GPIO_Pin_8);
		
		// 设置PWM
		TIM_SetCompare3(TIM4, rightspeed); // Motor3
		TIM_SetCompare4(TIM8, rightspeed); // Motor4
	}
}

void Move_stop(void)
{
	// 停止所有电机
	TIM_SetCompare3(TIM3, 0); // Motor1
	TIM_SetCompare4(TIM3, 0); // Motor2
	TIM_SetCompare3(TIM4, 0); // Motor3
	TIM_SetCompare4(TIM8, 0); // Motor4
}

void Car_Retreat(void)
{
     Set_Motor(-TURNCAR, TURNCAR);
}

void Car_Letreat(void)
{
     Set_Motor(TURNCAR, -TURNCAR);
}

/**************************************************************************
功能：编码器初始化函数
入口参数：无
返回  值：无
说明：仅使用TIM2作为编码器
**************************************************************************/
void Encoder_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStrue;
	
	// 使能GPIO和定时器时钟
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
	
	// 配置TIM2编码器引脚：PA0(TIM2_CH1)、PA1(TIM2_CH2)
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING; // 编码器输入配置为浮空输入
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);
	
	// 配置TIM2为编码器模式
	TIM_TimeBaseInitStrue.TIM_Period = 0xFFFF; // 16位最大计数值
	TIM_TimeBaseInitStrue.TIM_Prescaler = 0;   // 不分频
	TIM_TimeBaseInitStrue.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStrue.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInit(TIM2, &TIM_TimeBaseInitStrue);
	
	// 设置TIM2为编码器接口模式
	TIM_EncoderInterfaceConfig(TIM2, TIM_EncoderMode_TI12,   TIM_ICPolarity_Rising, TIM_ICPolarity_Rising);
	
	// 清零计数器
	TIM_SetCounter(TIM2, 0);
	
	// 使能定时器
	TIM_Cmd(TIM2, ENABLE);
}

/**************************************************************************
功能：获取编码器计数值
入口参数：无
返回  值：编码器计数值（有符号，支持正负方向）
说明：TIM2编码器用于PID控制
**************************************************************************/
int32_t Get_Encoder_Count(void)
{
	// 直接返回uint16_t值，避免int16_t溢出问题
	// 对于编码器应用，通常不需要负值
	return (int32_t)TIM_GetCounter(TIM2);
}

/**************************************************************************
功能：重置编码器计数
入口参数：无
返回  值：无
说明：将TIM2计数器清零
**************************************************************************/
void Reset_Encoder(void)
{
	TIM_SetCounter(TIM2, 0);
}

