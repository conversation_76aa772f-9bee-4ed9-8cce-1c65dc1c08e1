#ifndef __TB6612_H 
#define __TB6612_H 
#include "sys.h"
#include "TIM8.h"
#include "TIM3.h"
#include "TIM4.h"


// 根据电路图修改引脚定义
// U3 TB6612
#define M1 PCout(5)   // AIN1
#define M2 PCout(4)   // AIN2
#define M3 PEout(7)   // BIN1
#define M4 PEout(8)   // BIN2

// U11 TB6612
#define M5 PDout(11)  // AIN1
#define M6 PDout(10)  // AIN2
#define M7 PDout(15)  // BIN1
#define M8 PCout(8)   // BIN2

void MOTOR_Init(void);

void TB6612_Init(int arr, int psc);
void SetPWM(int pwm);
void motor_mode(void);
void Set_Motor(s32 leftspeed,s32 rightspeed);
void Move_stop(void);
void Car_Retreat(void);
void Car_Letreat(void);

// 编码器相关函数声明
void Encoder_Init(void);           // 编码器初始化（仅TIM2）
int32_t Get_Encoder_Count(void);   // 获取编码器计数
void Reset_Encoder(void);          // 重置编码器计数

#endif 
